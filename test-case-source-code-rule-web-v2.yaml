# 案例检查规则配置

- name: 案例步骤
  match: "driver.stage("
  desc: "案例步骤参数校验"
  rules:
    # driver.stage(步骤名称, 步骤描述)
    - regexp: "driver.stage\\('[^']+',\\s*'[^']*'\\)"
      message: "步骤名称不能为空"

- name: 拷贝文件夹动作
  match: "driver.execute_action(action_name='CopyDir'"
  desc: "拷贝文件夹动作参数校验，源文件夹参数、目标文件夹参数、是否覆盖参数"
  rules:
    - regexp: "'SrcDir': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "源文件夹参数不符合规范"
    - regexp: "'DestDir': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "目标文件夹参数不符合规范"
    - regexp: "'IsOverwrite': (True|False)"
      message: "是否覆盖参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 拷贝文件动作
  match: "driver.execute_action(action_name='CopyFile'"
  desc: "拷贝文件动作参数校验，源文件参数、目标文件参数、是否覆盖参数"
  rules:
    - regexp: "'SrcFile': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "源文件参数不符合规范"
    - regexp: "'DestFile': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "目标文件参数不符合规范"
    - regexp: "'IsOverwrite': (True|False)"
      message: "是否覆盖参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 创建文件夹
  match: "driver.execute_action(action_name='CreateDir'"
  desc: "创建文件夹动作参数校验，文件夹参数"
  rules:
    - regexp: "'Dir': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件夹参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 删除文件夹
  match: "driver.execute_action(action_name='DeleteDir'"
  desc: "删除文件夹动作参数校验，文件夹参数、是否忽略失败参数"
  rules:
    - regexp: "'DirPath': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件夹参数不符合规范"
    - regexp: "'IgnoreFailed': (True|False)"
      message: "是否忽略失败不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 删除文件
  match: "driver.execute_action(action_name='DeleteFile'"
  desc: "删除文件动作参数校验，文件参数"
  rules:
    - regexp: "'FilePath': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 安装字体
  match: "driver.execute_action(action_name='InstallFont'"
  desc: "安装字体动作参数校验，字体文件路径参数"
  rules:
    - regexp: "'FontPath': '[^']*%[^%]*%[^']*'"
      message: "字体文件路径参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 卸载字体
  match: "driver.execute_action(action_name='UninstallFont'"
  desc: "卸载字体动作参数校验，字体参数"
  rules:
    - regexp: "'FontName': '[\\s\\S]*'"
      message: "字体参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 设置系统时间
  match: "driver.execute_action(action_name='SetSystemTime', desc='设置系统时间'"
  desc: "设置系统时间动作参数校验，设置系统时间类型参数、系统日期参数、系统时间参数"
  rules:
    - regexp: "'operationType': 'setLocalTime'"
      message: "设置时间参数不符合规范"
    - regexp: "'LocalDate': '\\d{4}-\\d{2}-\\d{2}'"
      message: "系统日期参数不符合规范"
    - regexp: "'LocalTime': '([01]\\d|2[0-3]):([0-5]\\d):([0-5]\\d)'"
      message: "系统时间参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 同步系统时间
  match: "driver.execute_action(action_name='SetSystemTime', desc='同步系统时间'"
  desc: "同步系统时间动作参数校验，设置系统时间类型参数"
  rules:
    - regexp: "'operationType': 'syncLocalTime'"
      message: "设置系统时间类型参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 复制崩溃Dump到指定目录
  match: "driver.execute_action(action_name='CopyWpsDump'"
  desc: "复制崩溃Dump到指定目录动作参数校验，目标文件参数"
  rules:
    - regexp: "'DestDir': '[^']*%[^%]*%[^']*'"
      message: "目标文件参数不符合规范"

- name: 打开文档
  match: "driver.execute_action(action_name='OpenDocument'"
  desc: "打开文档动作参数校验，文件路径参数"
  rules:
    - regexp: "'url': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件路径参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 运行JSFile文件中的Function
  match: "driver.execute_action(action_name='ExecuteJsFile'"
  desc: "运行JSFile文件中的Function动作参数校验，文件参数、js函数名参数"
  rules:
    - regexp: "'jsFile': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"
    - regexp: "'runFunctionName': '(Action\\d*)'"
      message: "方法名参数不符合规范"

- name: 运行JSFile文件中的Function检查点
  match: "driver.execute_checkpoint(checkpoint_name='ExecuteJsFile'"
  desc: "运行JSFile文件中的Function检查点动作参数校验，文件参数、方法名参数"
  rules:
    - regexp: "'jsFile': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"
    - regexp: "'runFunctionName': '(Check\\d*)'"
      message: "方法名参数不符合规范"
    - regexp: "is_must_pass=True"
      message: "没有开启必须通过"

- name: AutoIt打开文件对话框操作
  match: "driver.execute_action(action_name='AutoItOpenFileDlg'"
  desc: "AutoIt打开文件对话框操作动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: AutoIt另存文件对话框操作
  match: "driver.execute_action(action_name='AutoItSaveAsDlg'"
  desc: "AutoIt另存文件对话框操作动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

#20240531
- name: 鼠标光标截图
  match: "driver.execute_action(action_name='CaptureCursor'"
  desc: "鼠标光标截图动作参数校验，路径参数"
  rules:
    - regexp: "'CursorPath': '[^']*%[^%]*%[^']*.png'"
      message: "图片路径参数不符合规范"

- name: 截取外部窗口
  match: "driver.execute_action(action_name='CaptureWindow'"
  desc: "截取外部窗口动作参数校验，路径参数"
  rules:
    - regexp: "'destPath': '[^']*%[^%]*%[^']*.png'"
      message: "图片路径参数不符合规范"

- name: 系统区域范围截图
  match: "driver.execute_action(action_name='CaptureWindowByPos'"
  desc: "系统区域范围截图动作参数校验，路径参数"
  rules:
    - regexp: "'DestPath': '[^']*%[^%]*%[^']*.png'"
      message: "图片路径参数不符合规范"

- name: 清理文件夹
  match: "driver.execute_action(action_name='ClearDir'"
  desc: "清理文件夹动作参数校验，路径参数"
  rules:
    - regexp: "'DirPath': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件路径参数不符合规范"
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"

- name: 导出U1控件信息
  match: "driver.execute_action(action_name='ExportUiCommand'"
  desc: "导出U1控件信息动作参数校验，路径参数"
  rules:
    - regexp: "'fileName': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: 视图区坐标范围截图(WPS)
  match: "driver.execute_action(action_name='ExportViewToPic', desc='视图区坐标范围截图(WPS)'"
  desc: "视图区坐标范围截图(WPS)动作参数校验，坐标范围参数、路径参数"
  rules:
    - regexp: "'coords': '(.*?),(.*?):(.*?),(.*?)'"
      message: "坐标范围参数不符合规范"
    - regexp: "'picName': '[^']*%[^%]*%[^']*.png'"
      message: "文件路径参数不符合规范"

- name: 视图区坐标范围截图(WPP)
  match: "driver.execute_action(action_name='ExportViewToPic', desc='视图区坐标范围截图(WPP)'"
  desc: "视图区坐标范围截图(WPP)动作参数校验，坐标范围参数、路径参数"
  rules:
    - regexp: "'coords': '(.*?),(.*?):(.*?),(.*?)'"
      message: "坐标范围参数不符合规范"
    - regexp: "'picName': '[^']*%[^%]*%[^']*.png'"
      message: "文件路径参数不符合规范"

- name: 视图区坐标范围截图(ET)
  match: "driver.execute_action(action_name='ExportViewToPic', desc='视图区坐标范围截图(ET)'"
  desc: "视图区坐标范围截图(ET)动作参数校验，坐标范围参数、路径参数"
  rules:
    - regexp: "'range': '(.*?):(.*?)'"
      message: "坐标范围参数不符合规范"
    - regexp: "'file': '[^']*%[^%]*%[^']*.png'"
      message: "文件路径参数不符合规范"

- name: 导出WidgetItemView到xml
  match: "driver.execute_action(action_name='ExportWidgetView'"
  desc: "导出WidgetItemView到xml动作参数校验，路径参数"
  rules:
    - regexp: "'fileName': '[^']*%[^%]*%[^']*.xml'"
      message: "文件路径参数不符合规范"

- name: 安装包图标截图
  match: "driver.execute_action(action_name='GetIconFromExe'"
  desc: "安装包图标截图动作参数校验，程序路径参数、文件路径参数"
  rules:
    - regexp: "'exePath': '[^']*%[^%]*%[^']*'"
      message: "程序路径参数不符合规范"
    - regexp: "'iconPath': '[^']*%[^%]*%[^']*.png'"
      message: "文件路径参数不符合规范"

- name: 导入宏文件(MSO)
  match: "driver.execute_action(action_name='ImportMacro'"
  desc: "导入宏文件(MSO)动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: 导入宏文件
  match: "driver.execute_action(action_name='ImportVBMacro'"
  desc: "导入宏文件动作参数校验，路径参数"
  rules:
    - regexp: "'macroFilePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: WpsIoDump动作
  match: "driver.execute_action(action_name='IoDump'"
  desc: "WpsIoDump动作参数校验，路径参数"
  rules:
    - regexp: "'checkpointFileContent': '[^']*%[^%]*%[^']*'"
      message: "检查点xml文件路径参数不符合规范"
    - regexp: "'specCollectionObjectContent': '[^']*%[^%]*%[^']*'"
      message: "通用xml文件路径参数不符合规范"
    - regexp: "'resultXml': '[^']*%[^%]*%[^']*'"
      message: "结果xml文件路径参数不符合规范"

- name: 鼠标点击
  match: "driver.execute_action(action_name='MouseClick'"
  desc: "鼠标点击动作参数校验，鼠标点击类型参数"
  rules:
    - regexp: "'ClickType': '(left|middle|right)'"
      message: "鼠标点击类型参数不符合规范"

#2024
- name: 移动文件夹
  match: "driver.execute_action(action_name='MoveDir'"
  desc: "移动文件夹动作参数校验，路径参数"
  rules:
    - regexp: "'SrcPath': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "源路径参数不符合规范"
    - regexp: "'DestPath': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "目标路径参数不符合规范"

- name: 移动文件
  match: "driver.execute_action(action_name='MoveFile'"
  desc: "移动文件动作参数校验，路径参数"
  rules:
    - regexp: "'SrcFile': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "源路径参数不符合规范"
    - regexp: "'DestFile': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "目标路径参数不符合规范"

- name: com方式打开文档
  match: "driver.execute_action(action_name='Open'"
  desc: "com方式打开文档动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: 打开文档(APl)
  match: "driver.execute_action(action_name='OpenDocumentByApi'"
  desc: "打开文档(APl)动作参数校验，路径参数"
  rules:
    - regexp: "'url': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件路径参数不符合规范"

- name: QTSpy回放(extend_driver)
  match: "driver.execute_action(action_name='QTSpyReplay'"
  desc: "QTSpy回放(extend_driver)动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: 回放录制文件
  match: "driver.execute_action(action_name='ReplayFile'"
  desc: "移动文件夹动作参数校验，路径参数"
  rules:
    - regexp: "'uir_path': '.*\\.uir'"
      message: "文件路径参数不符合规范"

- name: 运行CefFile文件
  match: "driver.execute_action(action_name='RunCef'"
  desc: "运行CefFile文件动作参数校验，路径参数"
  rules:
    - regexp: "'stepsFilePath': '[^']*%[^%]*%[^']*'"
      message: "json文件路径参数不符合规范"
    - regexp: "'imageDir': '%CaseDir%'"
      message: "图片路径参数不符合规范"

- name: 运行工具
  match: "driver.execute_action(action_name='RunTool'"
  desc: "运行工具动作参数校验，路径参数"
  rules:
    - regexp: "'ToolPath': '.*'"
      message: "工具文件路径参数不符合规范"

- name: MSO另存为
  match: "driver.execute_action(action_name='SaveAs'"
  desc: "MSO另存为动作参数校验，路径参数"
  rules:
    - regexp: "'destPath': '[^']*%[^%]*%[^']*'"
      message: "目标路径参数不符合规范"

- name: 截图
  match: "driver.execute_action(action_name='ScreenCapture'"
  desc: "截图动作参数校验，路径参数"
  rules:
    - regexp: "'DestPath': '[^']*%[^%]*%[^']*.png'"
      message: "文件路径参数不符合规范"

- name: 根据WidgetName截图
  match: "driver.execute_action(action_name='ScreenShot'"
  desc: "根据WidgetName截图动作参数校验，路径参数"
  rules:
    - regexp: "'picture_path': '[^']*%[^%]*%[^']*.png'"
      message: "文件路径参数不符合规范"

- name: 设置CEF端口集合
  match: "driver.execute_action(action_name='SetCefPortList'"
  desc: "设置CEF端口集合动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: 设置剪切板
  match: "driver.execute_action(action_name='SetClipboard'"
  desc: "设置剪切板动作参数校验，路径参数"
  rules:
    - regexp: "'DataFormat': '(Text|Picture|Html)'"
      message: "数据格式参数不符合规范"

- name: 设置文件路径(QT)
  match: "driver.execute_action(action_name='SetFilePath'"
  desc: "设置文件路径(QT)动作参数校验，路径参数"
  rules:
    - regexp: "'file_path': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*|.*\\.\\w+)'"
      message: "文件路径参数不符合规范"

- name: 打印输出到文件
  match: "driver.execute_action(action_name='WpsPrintOut'"
  desc: "打印输出到文件动作参数校验，路径参数"
  rules:
    - regexp: "'FilePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: WpsSaveAs动作
  match: "driver.execute_action(action_name='WpsSaveAs'"
  desc: "WpsSaveAs动作动作参数校验，路径参数"
  rules:
    - regexp: "'FilePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"

- name: 检查文件或文件夹是否存在
  match: "driver.execute_checkpoint(checkpoint_name='FileExist'"
  desc: "检查文件或文件夹是否存在动作参数校验，路径参数"
  rules:
    - regexp: "'FilePath': '(C[^']*|D[^']*|E[^']*|[^']*%[^%]*%[^']*|\\\\\\\\[^']*|//[^']*)'"
      message: "文件路径参数不符合规范"
    - regexp: "is_must_pass=True"
      message: "没有开启必须通过"

- name: 图片对比
  match: "driver.execute_checkpoint(checkpoint_name='PictureCompare'"
  desc: "图片对比动作参数校验，路径参数"
  rules:
    - regexp: "'BasePath': '[^']*%[^%]*%[^']*'"
      message: "基准图片路径参数不符合规范"
    - regexp: "'CmpPath': '[^']*%[^%]*%[^']*'"
      message: "对比图片路径参数不符合规范"

- name: 图片文件夹对比
  match: "driver.execute_checkpoint(checkpoint_name='PictureDirCompare'"
  desc: "图片文件夹对比动作参数校验，路径参数"
  rules:
    - regexp: "'BaseLineDir': '[^']*%[^%]*%[^']*'"
      message: "基准图片文件夹路径参数不符合规范"
    - regexp: "'ResultDir': '[^']*%[^%]*%[^']*'"
      message: "对比图片文件夹路径参数不符合规范"

- name: 执行Python文件
  match: "driver.execute_checkpoint(action_name='RunPythonFile'"
  desc: "执行Python文件动作参数校验，路径参数"
  rules:
    - regexp: "'ToolPath': '[^']*%[^%]*%[^']*'"
      message: "Python文件路径参数不符合规范"

- name: 运行Python文件（检查点）
  match: "driver.execute_checkpoint(checkpoint_name='RunPythonFile'"
  desc: "运行Python文件（检查点）动作参数校验，路径参数"
  rules:
    - regexp: "'CodeFile': '[^']*%[^%]*%[^']*'"
      message: "Python文件路径参数不符合规范"

- name: XML对比
  match: "driver.execute_checkpoint(checkpoint_name='XmlCompare'"
  desc: "XML对比动作参数校验，路径参数"
  rules:
    - regexp: "'OriginalFileName': '[^']*%[^%]*%[^']*'"
      message: "基线xml文件路径参数不符合规范"
    - regexp: "'FinalFileName': '[^']*%[^%]*%[^']*'"
      message: "实际xml文件路径参数不符合规范"
    - regexp: "'DiffGramFileName': '[^']*%[^%]*%[^']*'"
      message: "差异xml文件路径参数不符合规范"

- name: 解压zip文件
  match: "driver.execute_action(action_name='UnZipFile'"
  desc: "解压zip文件动作参数校验，路径参数"
  rules:
    - regexp: "'filePath': '[^']*%[^%]*%[^']*'"
      message: "文件路径参数不符合规范"
    - regexp: "'dstDir': '[^']*%[^%]*%[^']*'"
      message: "目标路径参数不符合规范"

- name: 检查控件属性
  match: "checkpoint_name='CheckWidgetProperty'"
  desc: "检查控件属性动作参数校验"
  rules:
    - regexp: "is_must_pass=True"
      message: "没有开启必须通过"
