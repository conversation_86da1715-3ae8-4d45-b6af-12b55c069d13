# 案例检查规则配置

- name: 案例标签校验
  match: testCaseTagList
  desc: 案例标签校验
  rules:
    - regexp: '[\w\u4e00-\u9fff]+' # 匹配任意字符
      message: 缺少标签
#    - regexp: windows个人版 # 匹配Windows个人版, 注意大小写
#      message: 缺少windows个人版标签

- name: 创建云文档动作
  match: "driver.execute_action(action_name='CreateWoDoc'"
  desc: 创建云文档动作参数校验，GroupId、ParentId、Cookies、FileName等参数
  rules:
    # GroupId参数校验 - 为变量引入
    - regexp: "'GroupId': '[^']*'"
      message: "GroupId参数不符合规范，应为变量引入"
    # ParentId参数校验 - 为变量引入
    - regexp: "'ParentId': '[^']*'"
      message: "ParentId参数不符合规范，应为变量引入"
    # Cookies参数校验 - 为变量引入
    - regexp: "'Cookies': '[^']*'"
      message: "Cookies参数不符合规范，应为变量引入"
    # FileName参数校验 - 为当前案例编号
    - regexp: "'FileName': '[^']*'"
      message: "FileName参数不符合规范，应为当前案例编号"
    # 超时时间参数校验
    - regexp: "'Timeout': '120'"
      message: "超时时间应设置为120s"
    # 必须执行参数校验
    - regexp: "is_must_execute=True"
      message: "没有开启必须执行"
